const express = require('express');
const axios = require('axios');
const app = express();
const port = 3000; // Anda bisa ubah port ini

// Middleware untuk mengizinkan parsing JSON di body request
app.use(express.json());

// --- Konfigurasi API Data Saham Eksternal ---
// PENTING: Ganti ini dengan URL base dan API Key dari penyedia data saham yang Anda gunakan.
// Contoh penyedia data saham: Alpha Vantage, Financial Modeling Prep (FMP), IEX Cloud, dll.
// Masing-masing penyedia memiliki struktur data dan cara autentikasi yang berbeda.
const STOCK_API_BASE_URL = 'https://api.example.com/stock-data'; // Ganti ini!
const API_KEY = 'YOUR_API_KEY'; // Ganti ini dengan API key Anda!

// --- Endpoint untuk Mendapatkan Profil Perusahaan ---
app.get('/api/stock/:symbol/profile', async (req, res) => {
  const symbol = req.params.symbol.toUpperCase(); // Ambil simbol saham dari URL
  try {
    // Lakukan permintaan ke API data saham eksternal
    const response = await axios.get(
      `${STOCK_API_BASE_URL}/profile?symbol=${symbol}&apikey=${API_KEY}`,
    );
    res.json(response.data); // Kirim data profil yang diterima
  } catch (error) {
    console.error(`Error fetching profile for ${symbol}:`, error.message);
    if (error.response) {
      // Jika ada respons dari API eksternal (misal: 404 Not Found, 401 Unauthorized)
      res
        .status(error.response.status)
        .json({
          message:
            error.response.data.message || 'Failed to fetch stock profile',
        });
    } else {
      res.status(500).json({ message: 'Internal Server Error' });
    }
  }
});

// --- Endpoint untuk Mendapatkan Valuasi (PBV/PE/ROE) ---
app.get('/api/stock/:symbol/valuation', async (req, res) => {
  const symbol = req.params.symbol.toUpperCase();
  try {
    // Permintaan ke API eksternal untuk data fundamental (misalnya, income statement, balance sheet, dll.)
    // Struktur URL dan parameter akan sangat bergantung pada penyedia data saham Anda.
    const response = await axios.get(
      `${STOCK_API_BASE_URL}/financials?symbol=${symbol}&apikey=${API_KEY}`,
    );

    // --- Logika untuk menghitung PBV, PE, ROE (INI HANYA CONTOH) ---
    // Anda perlu menyesuaikan ini berdasarkan struktur data yang Anda terima dari API eksternal.
    // Asumsi: response.data mengandung informasi yang diperlukan.
    const stockData = response.data; // Data mentah dari API eksternal

    // Contoh perhitungan (ini akan sangat bervariasi):
    let pbv = null;
    let pe = null;
    let roe = null;

    // Contoh (Anda harus mengambil data yang relevan dari stockData)
    // Misalkan Anda mendapatkan:
    // const marketPrice = stockData.marketPrice;
    // const bookValuePerShare = stockData.bookValuePerShare;
    // const earningsPerShare = stockData.earningsPerShare;
    // const netIncome = stockData.netIncome;
    // const shareholdersEquity = stockData.shareholdersEquity;

    // if (marketPrice && bookValuePerShare) {
    //     pbv = marketPrice / bookValuePerShare;
    // }
    // if (marketPrice && earningsPerShare) {
    //     pe = marketPrice / earningsPerShare;
    // }
    // if (netIncome && shareholdersEquity) {
    //     roe = (netIncome / shareholdersEquity) * 100; // Dalam persen
    // }

    // Untuk tujuan demonstrasi, kita akan mengirimkan data mentah
    // Anda harus mengolah data yang diterima dari penyedia eksternal
    // untuk menghitung PBV, PE, ROE sesuai rumus.
    res.json({
      symbol: symbol,
      // pbv: pbv ? pbv.toFixed(2) : 'N/A',
      // pe: pe ? pe.toFixed(2) : 'N/A',
      // roe: roe ? roe.toFixed(2) : 'N/A',
      rawDataFromExternalAPI: stockData, // Anda bisa melihat struktur data mentah di sini
    });
  } catch (error) {
    console.error(`Error fetching valuation for ${symbol}:`, error.message);
    if (error.response) {
      res
        .status(error.response.status)
        .json({
          message:
            error.response.data.message || 'Failed to fetch stock valuation',
        });
    } else {
      res.status(500).json({ message: 'Internal Server Error' });
    }
  }
});

// --- Endpoint Umum untuk mendapatkan informasi lain (contoh) ---
// Anda bisa menambahkan endpoint lain seperti:
// - /api/stock/:symbol/historical-data
// - /api/stock/:symbol/dividends
// - dll.

// Contoh endpoint untuk mendapatkan data harga (jika disediakan oleh API eksternal)
app.get('/api/stock/:symbol/price', async (req, res) => {
  const symbol = req.params.symbol.toUpperCase();
  try {
    const response = await axios.get(
      `${STOCK_API_BASE_URL}/price?symbol=${symbol}&apikey=${API_KEY}`,
    );
    res.json(response.data);
  } catch (error) {
    console.error(`Error fetching price for ${symbol}:`, error.message);
    if (error.response) {
      res
        .status(error.response.status)
        .json({
          message: error.response.data.message || 'Failed to fetch stock price',
        });
    } else {
      res.status(500).json({ message: 'Internal Server Error' });
    }
  }
});

// Start server
app.listen(port, () => {
  console.log(`API server berjalan di http://localhost:${port}`);
  console.log(`Coba akses:`);
  console.log(`- Profil: http://localhost:${port}/api/stock/BBCA/profile`);
  console.log(`- Valuasi: http://localhost:${port}/api/stock/BBCA/valuation`);
  console.log(`- Harga: http://localhost:${port}/api/stock/BBCA/price`);
  console.log(`(Ganti 'BBCA' dengan simbol saham lain yang valid)`);
});
